/**
 * Gallery Bulk Operations API
 * Handles bulk operations for gallery items (upload, delete, update, etc.)
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';
import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({ error: authResult.error });
    }

    const supabaseAdmin = getAdminClient();

    switch (req.method) {
      case 'POST':
        return await handleBulkCreate(req, res, supabaseAdmin);
      case 'PUT':
        return await handleBulkUpdate(req, res, supabaseAdmin);
      case 'DELETE':
        return await handleBulkDelete(req, res, supabaseAdmin);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Gallery Bulk API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Bulk create gallery items
 */
async function handleBulkCreate(req, res, supabaseAdmin) {
  try {
    const { operation, items } = req.body;

    if (operation !== 'create' || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        error: 'Invalid request. Expected operation: "create" and items array'
      });
    }

    // Validate each item
    const validatedItems = [];
    const errors = [];

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      if (!item.title || !item.category || !item.main_image_url) {
        errors.push(`Item ${i + 1}: Missing required fields (title, category, main_image_url)`);
        continue;
      }

      // Verify category exists
      const { data: categoryExists } = await supabaseAdmin
        .from('gallery_categories')
        .select('id')
        .eq('slug', item.category)
        .single();

      if (!categoryExists) {
        errors.push(`Item ${i + 1}: Invalid category "${item.category}"`);
        continue;
      }

      validatedItems.push({
        title: item.title.trim(),
        description: item.description?.trim() || '',
        category: item.category,
        main_image_url: item.main_image_url,
        gallery_images: Array.isArray(item.gallery_images) ? item.gallery_images : [],
        status: item.status || 'active',
        featured: Boolean(item.featured),
        display_order: parseInt(item.display_order) || 0,
        meta_title: item.meta_title?.trim() || item.title.trim(),
        meta_description: item.meta_description?.trim() || item.description?.trim() || ''
      });
    }

    if (errors.length > 0) {
      return res.status(400).json({
        error: 'Validation errors',
        details: errors
      });
    }

    // Insert all valid items
    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .insert(validatedItems)
      .select();

    if (error) {
      console.error('Error bulk creating gallery items:', error);
      return res.status(500).json({ error: 'Failed to create gallery items' });
    }

    return res.status(201).json({
      success: true,
      data,
      message: `Successfully created ${data.length} gallery items`,
      created_count: data.length,
      error_count: errors.length
    });
  } catch (error) {
    console.error('Error in handleBulkCreate:', error);
    return res.status(500).json({ error: 'Failed to bulk create gallery items' });
  }
}

/**
 * Bulk update gallery items
 */
async function handleBulkUpdate(req, res, supabaseAdmin) {
  try {
    const { operation, updates } = req.body;

    if (operation !== 'update' || !updates) {
      return res.status(400).json({
        error: 'Invalid request. Expected operation: "update" and updates object'
      });
    }

    const { ids, data: updateData } = updates;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ error: 'No item IDs provided' });
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return res.status(400).json({ error: 'No update data provided' });
    }

    // Build update object
    const validUpdateData = {};

    if (updateData.status !== undefined) validUpdateData.status = updateData.status;
    if (updateData.featured !== undefined) validUpdateData.featured = Boolean(updateData.featured);
    if (updateData.category !== undefined) validUpdateData.category = updateData.category;
    if (updateData.display_order !== undefined) validUpdateData.display_order = parseInt(updateData.display_order) || 0;

    validUpdateData.updated_at = new Date().toISOString();

    // Update all items with the provided IDs
    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .update(validUpdateData)
      .in('id', ids)
      .select();

    if (error) {
      console.error('Error bulk updating gallery items:', error);
      return res.status(500).json({ error: 'Failed to update gallery items' });
    }

    return res.status(200).json({
      success: true,
      data,
      message: `Successfully updated ${data.length} gallery items`,
      updated_count: data.length
    });
  } catch (error) {
    console.error('Error in handleBulkUpdate:', error);
    return res.status(500).json({ error: 'Failed to bulk update gallery items' });
  }
}

/**
 * Bulk delete gallery items
 */
async function handleBulkDelete(req, res, supabaseAdmin) {
  try {
    const { operation, ids } = req.body;

    if (operation !== 'delete' || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        error: 'Invalid request. Expected operation: "delete" and ids array'
      });
    }

    // Delete all items with the provided IDs
    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .delete()
      .in('id', ids)
      .select();

    if (error) {
      console.error('Error bulk deleting gallery items:', error);
      return res.status(500).json({ error: 'Failed to delete gallery items' });
    }

    return res.status(200).json({
      success: true,
      data,
      message: `Successfully deleted ${data.length} gallery items`,
      deleted_count: data.length
    });
  } catch (error) {
    console.error('Error in handleBulkDelete:', error);
    return res.status(500).json({ error: 'Failed to bulk delete gallery items' });
  }
}

/**
 * Reorder gallery items
 */
async function handleBulkReorder(req, res, supabaseAdmin) {
  try {
    const { operation, items } = req.body;

    if (operation !== 'reorder' || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        error: 'Invalid request. Expected operation: "reorder" and items array'
      });
    }

    // Validate items have id and display_order
    const updates = items.map((item, index) => {
      if (!item.id) {
        throw new Error(`Item at index ${index} missing id`);
      }
      return {
        id: item.id,
        display_order: item.display_order !== undefined ? parseInt(item.display_order) : index,
        updated_at: new Date().toISOString()
      };
    });

    // Update each item's display order
    const updatePromises = updates.map(update =>
      supabaseAdmin
        .from('gallery_items')
        .update({
          display_order: update.display_order,
          updated_at: update.updated_at
        })
        .eq('id', update.id)
        .select()
        .single()
    );

    const results = await Promise.allSettled(updatePromises);

    const successful = results.filter(result => result.status === 'fulfilled').map(result => result.value.data);
    const failed = results.filter(result => result.status === 'rejected');

    if (failed.length > 0) {
      console.error('Some reorder operations failed:', failed);
    }

    return res.status(200).json({
      success: true,
      data: successful,
      message: `Successfully reordered ${successful.length} gallery items`,
      updated_count: successful.length,
      failed_count: failed.length
    });
  } catch (error) {
    console.error('Error in handleBulkReorder:', error);
    return res.status(500).json({ error: 'Failed to reorder gallery items' });
  }
}
