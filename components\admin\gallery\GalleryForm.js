import { useState, useEffect } from 'react';
import { getAuthToken } from '@/lib/auth-token-manager';
import { safeRender } from '@/lib/safe-render-utils';
import { toast } from 'react-toastify';
import ImagePicker from '@/components/admin/ImagePicker';
import styles from '@/styles/admin/inventory/ProductForm.module.css'; // Reuse existing styles

export default function GalleryForm({ item, onSave, onCancel, loading }) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    main_image_url: '',
    gallery_images: [],
    status: 'active',
    featured: false,
    display_order: 0,
    meta_title: '',
    meta_description: ''
  });

  const [categories, setCategories] = useState([]);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data when item prop changes
  useEffect(() => {
    if (item) {
      setFormData({
        title: safeRender(item.title, ''),
        description: safeRender(item.description, ''),
        category: safeRender(item.category, ''),
        main_image_url: safeRender(item.main_image_url, ''),
        gallery_images: item.gallery_images || [],
        status: safeRender(item.status, 'active'),
        featured: item.featured || false,
        display_order: item.display_order || 0,
        meta_title: safeRender(item.meta_title, ''),
        meta_description: safeRender(item.meta_description, '')
      });
    }
  }, [item]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const token = await getAuthToken();
        const response = await fetch('/api/admin/gallery/categories', {
          headers: {
            'Authorization': `Bearer ${token || ''}`,
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setCategories(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // Handle image upload
  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please select a valid image file (JPEG, PNG, or WebP)');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image file size must be less than 5MB');
      return;
    }

    setUploadingImage(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const token = await getAuthToken();

      const response = await fetch('/api/admin/uploads/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token || ''}`,
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      handleInputChange('main_image_url', data.url);
      toast.success('Image uploaded successfully!');
    } catch (err) {
      console.error('Error uploading image:', err);
      toast.error('Failed to upload image. Please try again.');
    } finally {
      setUploadingImage(false);
    }
  };

  // Handle gallery images
  const handleAddGalleryImage = (imageUrl) => {
    const newImage = {
      src: imageUrl,
      alt: formData.title || 'Gallery image',
      caption: formData.title || 'Gallery image'
    };

    setFormData(prev => ({
      ...prev,
      gallery_images: [...prev.gallery_images, newImage]
    }));
  };

  const handleRemoveGalleryImage = (index) => {
    setFormData(prev => ({
      ...prev,
      gallery_images: prev.gallery_images.filter((_, i) => i !== index)
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.main_image_url) {
      newErrors.main_image_url = 'Main image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors below');
      return;
    }

    // Auto-generate meta fields if not provided
    const submitData = {
      ...formData,
      meta_title: formData.meta_title || formData.title,
      meta_description: formData.meta_description || formData.description || `${formData.title} - Professional face and body art by OceanSoulSparkles`
    };

    onSave(submitData);
  };

  return (
    <div className={styles.formContainer}>
      <h2>{item ? 'Edit Gallery Item' : 'Add Gallery Item'}</h2>

      <form onSubmit={handleSubmit} className={styles.form}>
        {/* Basic Information */}
        <div className={styles.formSection}>
          <h3>Basic Information</h3>

          <div className={styles.formGroup}>
            <label htmlFor="title">Title *</label>
            <input
              type="text"
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className={errors.title ? styles.error : ''}
              placeholder="Enter gallery item title"
            />
            {errors.title && <span className={styles.errorText}>{errors.title}</span>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter description"
              rows={4}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="category">Category *</label>
            <select
              id="category"
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className={errors.category ? styles.error : ''}
            >
              <option value="">Select a category</option>
              {categories.map(category => (
                <option key={category.slug} value={category.slug}>
                  {safeRender(category.name)}
                </option>
              ))}
            </select>
            {errors.category && <span className={styles.errorText}>{errors.category}</span>}
          </div>
        </div>

        {/* Main Image */}
        <div className={styles.formSection}>
          <h3>Main Image *</h3>

          {formData.main_image_url && (
            <div className={styles.currentImage}>
              <img
                src={formData.main_image_url}
                alt="Current main image"
                className={styles.previewImage}
              />
            </div>
          )}

          <div className={styles.imageUploadOptions}>
            <div className={styles.uploadOption}>
              <input
                type="file"
                id="main-image-upload"
                accept="image/jpeg,image/png,image/webp"
                onChange={handleImageUpload}
                disabled={uploadingImage}
                className={styles.fileInput}
                style={{ display: 'none' }}
              />
              <label htmlFor="main-image-upload" className={styles.uploadButton}>
                {uploadingImage ? 'Uploading...' : '📁 Upload New Image'}
              </label>
            </div>
            <div className={styles.uploadOption}>
              <button
                type="button"
                onClick={() => setShowImagePicker(true)}
                className={styles.selectImageButton}
                disabled={uploadingImage}
              >
                🖼️ Select Existing Image
              </button>
            </div>
          </div>

          {errors.main_image_url && <span className={styles.errorText}>{errors.main_image_url}</span>}
        </div>

        {/* Gallery Images */}
        <div className={styles.formSection}>
          <h3>Additional Gallery Images</h3>

          {formData.gallery_images.length > 0 && (
            <div className={styles.galleryImages}>
              {formData.gallery_images.map((image, index) => (
                <div key={index} className={styles.galleryImageItem}>
                  <img src={image.src} alt={image.alt} className={styles.galleryThumbnail} />
                  <button
                    type="button"
                    onClick={() => handleRemoveGalleryImage(index)}
                    className={styles.removeImageButton}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}

          <button
            type="button"
            onClick={() => setShowImagePicker(true)}
            className={styles.addImageButton}
          >
            + Add Gallery Image
          </button>
        </div>

        {/* Settings */}
        <div className={styles.formSection}>
          <h3>Settings</h3>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="status">Status</label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="draft">Draft</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="display_order">Display Order</label>
              <input
                type="number"
                id="display_order"
                value={formData.display_order}
                onChange={(e) => handleInputChange('display_order', parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
          </div>

          <div className={styles.formGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={formData.featured}
                onChange={(e) => handleInputChange('featured', e.target.checked)}
              />
              Featured Item
            </label>
          </div>
        </div>

        {/* SEO */}
        <div className={styles.formSection}>
          <h3>SEO (Optional)</h3>

          <div className={styles.formGroup}>
            <label htmlFor="meta_title">Meta Title</label>
            <input
              type="text"
              id="meta_title"
              value={formData.meta_title}
              onChange={(e) => handleInputChange('meta_title', e.target.value)}
              placeholder="Auto-generated from title if empty"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="meta_description">Meta Description</label>
            <textarea
              id="meta_description"
              value={formData.meta_description}
              onChange={(e) => handleInputChange('meta_description', e.target.value)}
              placeholder="Auto-generated from description if empty"
              rows={3}
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className={styles.formActions}>
          <button
            type="button"
            onClick={onCancel}
            className={styles.cancelButton}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={styles.saveButton}
            disabled={loading || uploadingImage}
          >
            {loading ? 'Saving...' : (item ? 'Update Gallery Item' : 'Create Gallery Item')}
          </button>
        </div>
      </form>

      {/* Image Picker Modal */}
      {showImagePicker && (
        <ImagePicker
          onSelect={(imagePath) => {
            if (formData.main_image_url) {
              // If main image exists, add to gallery
              handleAddGalleryImage(imagePath);
            } else {
              // If no main image, set as main image
              handleInputChange('main_image_url', imagePath);
            }
            setShowImagePicker(false);
          }}
          onCancel={() => setShowImagePicker(false)}
          currentImage={formData.main_image_url}
          directory="gallery"
          title="Select Gallery Image"
        />
      )}
    </div>
  );
}
