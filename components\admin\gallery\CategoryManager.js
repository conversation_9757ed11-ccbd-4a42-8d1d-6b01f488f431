import { useState, useEffect } from 'react';
import { getAuthToken } from '@/lib/auth-token-manager';
import { safeRender } from '@/lib/safe-render-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/ProductList.module.css'; // Reuse existing styles

export default function CategoryManager({
  category,
  onSave,
  onCancel,
  loading,
  isModal = false,
  refreshKey,
  onEditCategory,
  onDeleteCategory
}) {
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#6a0dad',
    display_order: 0,
    status: 'active'
  });
  const [errors, setErrors] = useState({});

  // Initialize form data when category prop changes (for modal mode)
  useEffect(() => {
    if (category && isModal) {
      setFormData({
        name: safeRender(category.name, ''),
        slug: safeRender(category.slug, ''),
        description: safeRender(category.description, ''),
        color: safeRender(category.color, '#6a0dad'),
        display_order: category.display_order || 0,
        status: safeRender(category.status, 'active')
      });
    }
  }, [category, isModal]);

  // Fetch categories (for list mode)
  useEffect(() => {
    if (!isModal) {
      fetchCategories();
    }
  }, [isModal, refreshKey]);

  const fetchCategories = async () => {
    try {
      setCategoriesLoading(true);
      const token = await getAuthToken();

      const response = await fetch('/api/admin/gallery/categories?include_counts=true', {
        headers: {
          'Authorization': `Bearer ${token || ''}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();
      setCategories(data.data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-generate slug from name
    if (field === 'name') {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();

      setFormData(prev => ({
        ...prev,
        slug: slug
      }));
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'Slug is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = 'Slug can only contain lowercase letters, numbers, and hyphens';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission (for modal mode)
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors below');
      return;
    }

    onSave(formData);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '#6a0dad',
      display_order: 0,
      status: 'active'
    });
    setErrors({});
  };

  // If this is a modal, render the form
  if (isModal) {
    return (
      <div className={styles.formContainer}>
        <h2>{category ? 'Edit Category' : 'Add Category'}</h2>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="name">Name *</label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={errors.name ? styles.error : ''}
              placeholder="Enter category name"
            />
            {errors.name && <span className={styles.errorText}>{errors.name}</span>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="slug">Slug *</label>
            <input
              type="text"
              id="slug"
              value={formData.slug}
              onChange={(e) => handleInputChange('slug', e.target.value)}
              className={errors.slug ? styles.error : ''}
              placeholder="category-slug"
            />
            {errors.slug && <span className={styles.errorText}>{errors.slug}</span>}
            <small>Used in URLs. Auto-generated from name.</small>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter category description"
              rows={3}
            />
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="color">Color</label>
              <input
                type="color"
                id="color"
                value={formData.color}
                onChange={(e) => handleInputChange('color', e.target.value)}
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="display_order">Display Order</label>
              <input
                type="number"
                id="display_order"
                value={formData.display_order}
                onChange={(e) => handleInputChange('display_order', parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="status">Status</label>
              <select
                id="status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div className={styles.formActions}>
            <button
              type="button"
              onClick={onCancel}
              className={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={styles.saveButton}
              disabled={loading}
            >
              {loading ? 'Saving...' : (category ? 'Update Category' : 'Create Category')}
            </button>
          </div>
        </form>
      </div>
    );
  }

  // Otherwise, render the categories list
  return (
    <div className={styles.productList}>
      <div className={styles.header}>
        <h3>Gallery Categories</h3>
      </div>

      {categoriesLoading ? (
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading categories...</p>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.table}>
            <thead>
              <tr>
                <th>Color</th>
                <th>Name</th>
                <th>Slug</th>
                <th>Description</th>
                <th>Items</th>
                <th>Order</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {categories.map((cat) => (
                <tr key={cat.id}>
                  <td>
                    <div
                      className={styles.colorSwatch}
                      style={{ backgroundColor: cat.color }}
                      title={cat.color}
                    ></div>
                  </td>
                  <td>
                    <strong>{safeRender(cat.name)}</strong>
                  </td>
                  <td>
                    <code>{safeRender(cat.slug)}</code>
                  </td>
                  <td>
                    {cat.description ? (
                      <span title={cat.description}>
                        {cat.description.length > 50
                          ? `${cat.description.substring(0, 50)}...`
                          : cat.description
                        }
                      </span>
                    ) : (
                      <em>No description</em>
                    )}
                  </td>
                  <td>
                    <span className={styles.itemCount}>
                      {cat.item_count || 0} items
                    </span>
                  </td>
                  <td>{cat.display_order}</td>
                  <td>
                    <span className={`${styles.statusBadge} ${styles[cat.status]}`}>
                      {safeRender(cat.status)}
                    </span>
                  </td>
                  <td>
                    <div className={styles.actionButtons}>
                      <button
                        onClick={() => onEditCategory(cat)}
                        className={styles.editButton}
                        title="Edit"
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => onDeleteCategory(cat)}
                        className={styles.deleteButton}
                        title="Delete"
                        disabled={cat.item_count > 0}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {categories.length === 0 && (
            <div className={styles.emptyState}>
              <p>No categories found.</p>
              <p>Add some categories to organize your gallery items.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
