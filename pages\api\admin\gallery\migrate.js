/**
 * Gallery Data Migration API
 * Migrates existing hardcoded gallery data to database
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';
import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
    }

    const supabaseAdmin = getAdminClient();

    // Check if tables exist and create them if needed
    await ensureTablesExist(supabaseAdmin);

    // Create default categories
    await createDefaultCategories(supabaseAdmin);

    // Migrate gallery data
    const migrationResult = await migrateGalleryData(supabaseAdmin);

    return res.status(200).json({
      success: true,
      message: 'Gallery data migration completed successfully',
      ...migrationResult
    });
  } catch (error) {
    console.error('Gallery migration error:', error);
    return res.status(500).json({ error: 'Migration failed: ' + error.message });
  }
}

/**
 * Ensure required tables exist
 */
async function ensureTablesExist(supabaseAdmin) {
  try {
    // Create gallery_categories table
    const categoriesTableSQL = `
      CREATE TABLE IF NOT EXISTS gallery_categories (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        color VARCHAR(7) DEFAULT '#6a0dad',
        display_order INTEGER DEFAULT 0,
        status VARCHAR(20) DEFAULT 'active',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // Create gallery_items table
    const itemsTableSQL = `
      CREATE TABLE IF NOT EXISTS gallery_items (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(255) NOT NULL,
        main_image_url TEXT NOT NULL,
        gallery_images JSONB DEFAULT '[]',
        status VARCHAR(20) DEFAULT 'active',
        featured BOOLEAN DEFAULT false,
        display_order INTEGER DEFAULT 0,
        meta_title VARCHAR(255),
        meta_description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        FOREIGN KEY (category) REFERENCES gallery_categories(slug)
      );
    `;

    // Execute table creation (this will be handled by Supabase migrations in production)
    console.log('Tables should be created via Supabase migrations');

    return true;
  } catch (error) {
    console.error('Error ensuring tables exist:', error);
    throw error;
  }
}

/**
 * Create default gallery categories
 */
async function createDefaultCategories(supabaseAdmin) {
  const defaultCategories = [
    {
      name: 'All',
      slug: 'all',
      description: 'All gallery items',
      color: '#6a0dad',
      display_order: 0
    },
    {
      name: 'Face Art',
      slug: 'face',
      description: 'Face painting and artistic designs',
      color: '#ff6b6b',
      display_order: 1
    },
    {
      name: 'Body Art',
      slug: 'body',
      description: 'Body painting and artistic designs',
      color: '#4ecdc4',
      display_order: 2
    },
    {
      name: 'UV Art',
      slug: 'uv',
      description: 'UV reactive and glow-in-the-dark designs',
      color: '#45b7d1',
      display_order: 3
    },
    {
      name: 'Kids Designs',
      slug: 'kids',
      description: 'Fun designs perfect for children',
      color: '#96ceb4',
      display_order: 4
    },
    {
      name: 'Events',
      slug: 'events',
      description: 'Event photography and special occasions',
      color: '#feca57',
      display_order: 5
    }
  ];

  try {
    // Check if categories already exist
    const { data: existingCategories } = await supabaseAdmin
      .from('gallery_categories')
      .select('slug');

    const existingSlugs = existingCategories?.map(cat => cat.slug) || [];
    const categoriesToCreate = defaultCategories.filter(cat => !existingSlugs.includes(cat.slug));

    if (categoriesToCreate.length > 0) {
      const { data, error } = await supabaseAdmin
        .from('gallery_categories')
        .insert(categoriesToCreate)
        .select();

      if (error) {
        console.error('Error creating default categories:', error);
        throw error;
      }

      console.log(`Created ${data.length} default categories`);
      return data;
    }

    console.log('Default categories already exist');
    return [];
  } catch (error) {
    console.error('Error in createDefaultCategories:', error);
    throw error;
  }
}

/**
 * Migrate existing gallery data
 */
async function migrateGalleryData(supabaseAdmin) {
  // Hardcoded gallery data from the current gallery.js file
  const galleryData = [
    {
      id: 'face-art-1',
      title: 'Butterfly Face Art',
      category: 'face',
      mainImage: '/images/gallery/fav/Butterfly.JPG'
    },
    {
      id: 'face-art-2',
      title: 'Gold Leopard',
      category: 'face',
      mainImage: '/images/gallery/fav/Gold Leopard.jpg'
    },
    {
      id: 'face-art-3',
      title: 'Soft Face',
      category: 'face',
      mainImage: '/images/gallery/fav/Soft Face.JPG'
    },
    {
      id: 'face-art-4',
      title: 'Blue Sparkles',
      category: 'face',
      mainImage: '/images/gallery/fav/Blue Sparkles.JPG'
    },
    {
      id: 'face-art-5',
      title: 'Glitter Goddess',
      category: 'face',
      mainImage: '/images/gallery/fav/Glitter Goddess.JPG'
    },
    {
      id: 'face-art-6',
      title: 'Glitter Goddess 2',
      category: 'face',
      mainImage: '/images/gallery/fav/Glitter Goddess 2.JPG'
    },
    {
      id: 'face-art-7',
      title: 'Glitter Goddess 3',
      category: 'face',
      mainImage: '/images/gallery/fav/Glitter Goddess 3.JPG'
    },
    {
      id: 'face-art-8',
      title: 'Camo Face Paint',
      category: 'face',
      mainImage: '/images/gallery/fav/Copy of Camo Bodypaint.JPG'
    },
    {
      id: 'body-art-1',
      title: 'Adult Body Suit',
      category: 'body',
      mainImage: '/images/gallery/fav/Adult BodySuit.JPG'
    },
    {
      id: 'body-art-2',
      title: 'Camo Body Paint',
      category: 'body',
      mainImage: '/images/gallery/fav/Camo Bodypaint.JPG'
    },
    {
      id: 'body-art-3',
      title: 'Camo Body Paint 2',
      category: 'body',
      mainImage: '/images/gallery/fav/Camo Bodypaint 2.JPG'
    },
    {
      id: 'uv-art-1',
      title: 'UV Alex',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Alex.JPG'
    },
    {
      id: 'uv-art-2',
      title: 'UV Body Paint',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Body Paint.JPG'
    },
    {
      id: 'uv-art-3',
      title: 'UV Body Paint 2',
      category: 'uv',
      mainImage: '/images/gallery/fav/UV Body Paint 2.JPG'
    },
    {
      id: 'kids-1',
      title: 'Kids Butterfly',
      category: 'kids',
      mainImage: '/images/gallery/fav/Kids Butterfly.jpg'
    },
    {
      id: 'kids-2',
      title: 'Kids Tropical',
      category: 'kids',
      mainImage: '/images/gallery/fav/Kids Tropical.jpg'
    },
    {
      id: 'kids-3',
      title: 'Toby Rainbow',
      category: 'kids',
      mainImage: '/images/gallery/fav/Toby Rainbow.JPG'
    },
    {
      id: 'events-1',
      title: 'OceanSoulVibes Light Box',
      category: 'events',
      mainImage: '/images/gallery/fav/OceanSoulVibes Light Box.JPG'
    },
    {
      id: 'events-2',
      title: 'OceanSoulVibes Light Box 2',
      category: 'events',
      mainImage: '/images/gallery/fav/OceanSoulVibes Light Box 2.JPG'
    },
    {
      id: 'events-3',
      title: 'Strawberry Icepoles Camo Facepaint',
      category: 'events',
      mainImage: '/images/gallery/fav/Strawberry Icepoles Camo Facepaint .jpeg'
    }
  ];

  try {
    // Check if gallery items already exist
    const { data: existingItems } = await supabaseAdmin
      .from('gallery_items')
      .select('title');

    if (existingItems && existingItems.length > 0) {
      return {
        categories_created: 0,
        items_migrated: 0,
        message: 'Gallery items already exist, skipping migration'
      };
    }

    // Transform data for database insertion
    const itemsToInsert = galleryData.map((item, index) => ({
      title: item.title,
      description: `Beautiful ${item.category} art design`,
      category: item.category,
      main_image_url: item.mainImage,
      gallery_images: [
        {
          src: item.mainImage,
          alt: item.title,
          caption: item.title
        }
      ],
      status: 'active',
      featured: index < 6, // Mark first 6 as featured
      display_order: index,
      meta_title: item.title,
      meta_description: `${item.title} - Professional face and body art by OceanSoulSparkles`
    }));

    // Insert gallery items
    const { data, error } = await supabaseAdmin
      .from('gallery_items')
      .insert(itemsToInsert)
      .select();

    if (error) {
      console.error('Error migrating gallery items:', error);
      throw error;
    }

    return {
      items_migrated: data.length,
      message: `Successfully migrated ${data.length} gallery items`
    };
  } catch (error) {
    console.error('Error in migrateGalleryData:', error);
    throw error;
  }
}
