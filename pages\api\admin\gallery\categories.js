/**
 * Gallery Categories Management API
 * Handles CRUD operations for gallery categories
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';
import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.success) {
      return res.status(401).json({ error: authResult.error });
    }

    const supabaseAdmin = getAdminClient();

    switch (req.method) {
      case 'GET':
        return await handleGetCategories(req, res, supabaseAdmin);
      case 'POST':
        return await handleCreateCategory(req, res, supabaseAdmin);
      case 'PUT':
        return await handleUpdateCategory(req, res, supabaseAdmin);
      case 'DELETE':
        return await handleDeleteCategory(req, res, supabaseAdmin);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Gallery Categories API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get all gallery categories
 */
async function handleGetCategories(req, res, supabaseAdmin) {
  try {
    const { include_counts = false } = req.query;

    let query = supabaseAdmin
      .from('gallery_categories')
      .select('*')
      .order('display_order', { ascending: true });

    const { data: categories, error } = await query;

    if (error) {
      console.error('Error fetching gallery categories:', error);
      return res.status(500).json({ error: 'Failed to fetch gallery categories' });
    }

    // Optionally include item counts for each category
    if (include_counts === 'true') {
      const categoriesWithCounts = await Promise.all(
        categories.map(async (category) => {
          const { count } = await supabaseAdmin
            .from('gallery_items')
            .select('*', { count: 'exact', head: true })
            .eq('category', category.slug)
            .eq('status', 'active');

          return {
            ...category,
            item_count: count || 0
          };
        })
      );

      return res.status(200).json({
        success: true,
        data: categoriesWithCounts
      });
    }

    return res.status(200).json({
      success: true,
      data: categories || []
    });
  } catch (error) {
    console.error('Error in handleGetCategories:', error);
    return res.status(500).json({ error: 'Failed to fetch gallery categories' });
  }
}

/**
 * Create new gallery category
 */
async function handleCreateCategory(req, res, supabaseAdmin) {
  try {
    const {
      name,
      slug,
      description,
      color = '#6a0dad',
      display_order = 0,
      status = 'active'
    } = req.body;

    // Validation
    if (!name || !slug) {
      return res.status(400).json({
        error: 'Missing required fields: name and slug are required'
      });
    }

    // Check if slug already exists
    const { data: existingCategory } = await supabaseAdmin
      .from('gallery_categories')
      .select('id')
      .eq('slug', slug)
      .single();

    if (existingCategory) {
      return res.status(400).json({ error: 'Category slug already exists' });
    }

    const category = {
      name: name.trim(),
      slug: slug.toLowerCase().trim(),
      description: description?.trim() || '',
      color,
      display_order: parseInt(display_order) || 0,
      status
    };

    const { data, error } = await supabaseAdmin
      .from('gallery_categories')
      .insert([category])
      .select()
      .single();

    if (error) {
      console.error('Error creating gallery category:', error);
      return res.status(500).json({ error: 'Failed to create gallery category' });
    }

    return res.status(201).json({
      success: true,
      data,
      message: 'Gallery category created successfully'
    });
  } catch (error) {
    console.error('Error in handleCreateCategory:', error);
    return res.status(500).json({ error: 'Failed to create gallery category' });
  }
}

/**
 * Update existing gallery category
 */
async function handleUpdateCategory(req, res, supabaseAdmin) {
  try {
    const { id } = req.query;
    const {
      name,
      slug,
      description,
      color,
      display_order,
      status
    } = req.body;

    if (!id) {
      return res.status(400).json({ error: 'Category ID is required' });
    }

    // Build update object with only provided fields
    const updateData = {};

    if (name !== undefined) updateData.name = name.trim();
    if (slug !== undefined) {
      // Check if new slug conflicts with existing categories (excluding current)
      const { data: existingCategory } = await supabaseAdmin
        .from('gallery_categories')
        .select('id')
        .eq('slug', slug.toLowerCase().trim())
        .neq('id', id)
        .single();

      if (existingCategory) {
        return res.status(400).json({ error: 'Category slug already exists' });
      }

      updateData.slug = slug.toLowerCase().trim();
    }
    if (description !== undefined) updateData.description = description.trim();
    if (color !== undefined) updateData.color = color;
    if (display_order !== undefined) updateData.display_order = parseInt(display_order) || 0;
    if (status !== undefined) updateData.status = status;

    updateData.updated_at = new Date().toISOString();

    const { data, error } = await supabaseAdmin
      .from('gallery_categories')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating gallery category:', error);
      return res.status(500).json({ error: 'Failed to update gallery category' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Gallery category not found' });
    }

    return res.status(200).json({
      success: true,
      data,
      message: 'Gallery category updated successfully'
    });
  } catch (error) {
    console.error('Error in handleUpdateCategory:', error);
    return res.status(500).json({ error: 'Failed to update gallery category' });
  }
}

/**
 * Delete gallery category
 */
async function handleDeleteCategory(req, res, supabaseAdmin) {
  try {
    const { id } = req.query;

    if (!id) {
      return res.status(400).json({ error: 'Category ID is required' });
    }

    // Check if category has any gallery items
    const { count } = await supabaseAdmin
      .from('gallery_items')
      .select('*', { count: 'exact', head: true })
      .eq('category', id);

    if (count > 0) {
      return res.status(400).json({
        error: `Cannot delete category. It contains ${count} gallery items. Please move or delete the items first.`
      });
    }

    const { data, error } = await supabaseAdmin
      .from('gallery_categories')
      .delete()
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error deleting gallery category:', error);
      return res.status(500).json({ error: 'Failed to delete gallery category' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Gallery category not found' });
    }

    return res.status(200).json({
      success: true,
      message: 'Gallery category deleted successfully'
    });
  } catch (error) {
    console.error('Error in handleDeleteCategory:', error);
    return res.status(500).json({ error: 'Failed to delete gallery category' });
  }
}
